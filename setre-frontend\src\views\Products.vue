<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '../store/productStore'
import type { ProductFilter } from '../types/product'

const router = useRouter()
const productStore = useProductStore()

const searchTerm = ref('')
const showDeleteConfirm = ref(false)
const productToDelete = ref<string | null>(null)
const selectedProducts = ref<string[]>([])
const showBulkDeleteConfirm = ref(false)

// Yükleme işlemi
onMounted(async () => {
  await productStore.fetchProducts()
})

// Ürün filtreleme
const handleSearchChange = () => {
  productStore.setFilter({ searchTerm: searchTerm.value })
}

const handleFilterChange = (filter: Partial<ProductFilter>) => {
  productStore.setFilter(filter)
}

const resetFilters = () => {
  searchTerm.value = ''
  productStore.resetFilter()
}

// Ürün işlemleri
const navigateToProductDetails = (productId: string) => {
  router.push(`/products/${productId}`)
}

const navigateToNewProduct = () => {
  router.push('/products/new')
}

const confirmDeleteProduct = (productId: string) => {
  productToDelete.value = productId
  showDeleteConfirm.value = true
}

const deleteProduct = async () => {
  if (productToDelete.value) {
    try {
      await productStore.deleteProduct(productToDelete.value)
      showDeleteConfirm.value = false
      productToDelete.value = null
    } catch (error) {
      console.error('Ürün silme hatası:', error)
    }
  }
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  productToDelete.value = null
}

// Toplu işlemler
const toggleSelectAll = () => {
  if (selectedProducts.value.length === productStore.filteredProducts.length) {
    selectedProducts.value = []
  } else {
    selectedProducts.value = productStore.filteredProducts.map(product => product.id)
  }
}

const toggleSelectProduct = (productId: string) => {
  const index = selectedProducts.value.indexOf(productId)
  if (index === -1) {
    selectedProducts.value.push(productId)
  } else {
    selectedProducts.value.splice(index, 1)
  }
}

const confirmBulkDelete = () => {
  if (selectedProducts.value.length > 0) {
    showBulkDeleteConfirm.value = true
  }
}

const bulkDeleteProducts = async () => {
  try {
    await Promise.all(selectedProducts.value.map(id => productStore.deleteProduct(id)))
    selectedProducts.value = []
    showBulkDeleteConfirm.value = false
  } catch (error) {
    console.error('Toplu ürün silme hatası:', error)
  }
}

const cancelBulkDelete = () => {
  showBulkDeleteConfirm.value = false
}

// Stok durumu için etiket renkleri
const getStockStatusClass = (stockLevel: number) => {
  if (stockLevel <= 0) return 'status-out-of-stock'
  if (stockLevel < 10) return 'status-low-stock'
  return 'status-in-stock'
}

const getStockStatusLabel = (stockLevel: number) => {
  if (stockLevel <= 0) return 'Stokta Yok'
  if (stockLevel < 10) return 'Az Stok'
  return 'Stokta'
}

// Seçili ürün sayısı
const selectedCount = computed(() => selectedProducts.value.length)
const isAllSelected = computed(() => {
  return selectedProducts.value.length === productStore.filteredProducts.length && productStore.filteredProducts.length > 0
})
</script>

<template>
  <div class="products-container">
    <div class="products-header">
      <h1>Ürünler</h1>
      <div class="header-actions">
        <button
          v-if="selectedCount > 0"
          class="btn btn-danger"
          @click="confirmBulkDelete"
        >
          <span class="pi pi-trash"></span>
          {{ selectedCount }} Ürünü Sil
        </button>
        <button class="btn btn-primary" @click="navigateToNewProduct">
          <span class="pi pi-plus"></span>
          Yeni Ürün
        </button>
      </div>
    </div>

    <div class="card">
      <div class="filters-container">
        <div class="search-container">
          <span class="pi pi-search search-icon"></span>
          <input
            type="text"
            class="search-input"
            placeholder="Ürün ara..."
            v-model="searchTerm"
            @input="handleSearchChange"
          />
        </div>

        <div class="filters">
          <div class="filter-group">
            <label>Kategori:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ category: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="electronics">Elektronik</option>
              <option value="clothing">Giyim</option>
              <option value="home">Ev Eşyaları</option>
              <option value="food">Gıda</option>
              <option value="other">Diğer</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Stok Durumu:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ stockStatus: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="in_stock">Stokta</option>
              <option value="low_stock">Az Stok</option>
              <option value="out_of_stock">Stokta Yok</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Sıralama:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ sortBy: e.target.value })"
            >
              <option value="name">İsim</option>
              <option value="price">Fiyat</option>
              <option value="stock">Stok</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Yön:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ sortOrder: e.target.value })"
            >
              <option value="asc">Artan</option>
              <option value="desc">Azalan</option>
            </select>
          </div>

          <button class="btn" @click="resetFilters">
            <span class="pi pi-filter-slash"></span>
            Filtreleri Temizle
          </button>
        </div>
      </div>

      <div v-if="productStore.loading" class="loading-container">
        <span class="pi pi-spin pi-spinner"></span>
        <p>Ürünler yükleniyor...</p>
      </div>

      <div v-else-if="productStore.error" class="error-container">
        <span class="pi pi-exclamation-triangle"></span>
        <p>{{ productStore.error }}</p>
        <button class="btn btn-primary" @click="productStore.fetchProducts">
          Yeniden Dene
        </button>
      </div>

      <div v-else-if="productStore.filteredProducts.length === 0" class="empty-state">
        <span class="pi pi-box"></span>
        <p>Ürün bulunamadı</p>
        <button class="btn btn-primary" @click="navigateToNewProduct">
          Yeni Ürün Oluştur
        </button>
      </div>

      <div v-else class="products-table-container">
        <table class="products-table">
          <thead>
            <tr>
              <th class="checkbox-cell">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>Ürün Adı</th>
              <th>SKU</th>
              <th>Kategori</th>
              <th>Fiyat</th>
              <th>Stok</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="product in productStore.filteredProducts"
              :key="product.id"
              @click="navigateToProductDetails(product.id)"
              class="product-row"
            >
              <td class="checkbox-cell" @click.stop>
                <input
                  type="checkbox"
                  :checked="selectedProducts.includes(product.id)"
                  @change="toggleSelectProduct(product.id)"
                />
              </td>
              <td class="product-name">
                <div class="product-info">
                  <div class="product-image" v-if="product.imageUrl">
                    <img :src="product.imageUrl" :alt="product.name" />
                  </div>
                  <div v-else class="product-image placeholder">
                    <span class="pi pi-image"></span>
                  </div>
                  <span>{{ product.name }}</span>
                </div>
              </td>
              <td>{{ product.sku }}</td>
              <td>{{ product.category }}</td>
              <td class="product-price">
                {{ product.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
              </td>
              <td>
                <span class="status-badge" :class="getStockStatusClass(product.stockQuantity)">
                  {{ getStockStatusLabel(product.stockQuantity) }} ({{ product.stockQuantity }})
                </span>
              </td>
              <td class="actions-cell" @click.stop>
                <button class="btn-icon" title="Düzenle" @click.stop="navigateToProductDetails(product.id)">
                  <span class="pi pi-pencil"></span>
                </button>
                <button class="btn-icon" title="Sil" @click.stop="confirmDeleteProduct(product.id)">
                  <span class="pi pi-trash"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Tekli Silme Onay Modalı -->
    <div v-if="showDeleteConfirm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Ürün Silme Onayı</h3>
          <button class="modal-close" @click="cancelDelete">
            <span class="pi pi-times"></span>
          </button>
        </div>
        <div class="modal-body">
          <p>Bu ürünü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="cancelDelete">İptal</button>
          <button class="btn btn-danger" @click="deleteProduct">Sil</button>
        </div>
      </div>
    </div>

    <!-- Toplu Silme Onay Modalı -->
    <div v-if="showBulkDeleteConfirm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Toplu Ürün Silme Onayı</h3>
          <button class="modal-close" @click="cancelBulkDelete">
            <span class="pi pi-times"></span>
          </button>
        </div>
        <div class="modal-body">
          <p><strong>{{ selectedCount }}</strong> ürünü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="cancelBulkDelete">İptal</button>
          <button class="btn btn-danger" @click="bulkDeleteProducts">Sil</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.products-container {
  width: 100%;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.products-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group label {
  font-size: 0.75rem;
  color: #6B7280;
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi,
.error-container .pi,
.empty-state .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-container .pi {
  color: var(--error-color);
}

.products-table-container {
  overflow-x: auto;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.products-table th,
.products-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.products-table th {
  font-weight: 600;
  color: #6B7280;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.checkbox-cell {
  width: 40px;
  text-align: center;
}

.product-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.product-row:hover {
  background-color: #F9FAFB;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 0.25rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image.placeholder {
  color: #9CA3AF;
}

.product-price {
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-in-stock {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-low-stock {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
}

.status-out-of-stock {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--text-color);
}

.btn-icon .pi-pencil:hover {
  color: var(--primary-color);
}

.btn-icon .pi-trash:hover {
  color: var(--error-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
}

@media (max-width: 1024px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .products-table th:nth-child(3),
  .products-table td:nth-child(3),
  .products-table th:nth-child(4),
  .products-table td:nth-child(4) {
    display: none;
  }
}

@media (max-width: 640px) {
  .products-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    flex-direction: column;
  }

  .header-actions button {
    width: 100%;
  }
}
</style>